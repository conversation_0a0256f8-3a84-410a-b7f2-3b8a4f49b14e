import { NextRequest, NextResponse } from 'next/server';
import { getUserByUsername, getVerificationCode, markVerificationCodeAsUsed } from '@/lib/database';
import bcrypt from 'bcryptjs';

/**
 * POST /api/auth/signin - Authenticate user
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { username, password, recoveryKey, gmail, verificationCode } = body;

    // Validate required fields
    if (!username) {
      return NextResponse.json(
        { error: 'Username is required' },
        { status: 400 }
      );
    }

    // Get user from database
    const user = await getUserByUsername(username);
    if (!user) {
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      );
    }

    // Validate password is provided
    if (!password) {
      return NextResponse.json(
        { error: 'Password is required' },
        { status: 400 }
      );
    }

    // Verify password
    if (!user.password) {
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      );
    }

    const passwordMatch = await bcrypt.compare(password, user.password);
    if (!passwordMatch) {
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      );
    }

    // Handle different authentication flows based on user role
    if (user.role === 'admin') {
      // Admin users need recovery key
      if (!recoveryKey) {
        return NextResponse.json(
          { error: 'Recovery key is required for admin users' },
          { status: 400 }
        );
      }

      // Verify recovery key
      if (user.recovery_key !== recoveryKey) {
        return NextResponse.json(
          { error: 'Invalid credentials' },
          { status: 401 }
        );
      }
    } else if (user.role === 'regular') {
      // Regular users need gmail and verification code
      if (!gmail || !verificationCode) {
        return NextResponse.json(
          { error: 'Gmail and verification code are required for regular users' },
          { status: 400 }
        );
      }

      // Verify gmail matches user's gmail
      if (user.gmail !== gmail) {
        return NextResponse.json(
          { error: 'Invalid credentials' },
          { status: 401 }
        );
      }

      // Verify email verification code
      const verificationRecord = await getVerificationCode(user.gmail, verificationCode, 'signin');

      if (!verificationRecord) {
        return NextResponse.json(
          { error: 'Invalid or expired verification code' },
          { status: 400 }
        );
      }

      // Mark verification code as used
      await markVerificationCodeAsUsed(verificationRecord.id);
    } else {
      return NextResponse.json(
        { error: 'Invalid user role' },
        { status: 400 }
      );
    }

    // Authentication successful
    return NextResponse.json(
      { 
        message: 'Sign in successful',
        user: {
          id: user.id,
          username: user.username,
          role: user.role,
          gmail: user.gmail,
          created_at: user.created_at
        }
      },
      { status: 200 }
    );
    
  } catch (error) {
    console.error('Error during sign in:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
