import { NextRequest, NextResponse } from 'next/server';
import { 
  getVerificationCode, 
  markVerificationCodeAsUsed,
  deleteExpiredVerificationCodes 
} from '@/lib/database';

/**
 * POST /api/auth/verify-code - Verify email verification code
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, code, type } = body;

    // Validate required fields
    if (!email || !code || !type) {
      return NextResponse.json(
        { error: 'Email, code, and type are required' },
        { status: 400 }
      );
    }

    // Validate type
    if (type !== 'signup' && type !== 'signin') {
      return NextResponse.json(
        { error: 'Type must be either "signup" or "signin"' },
        { status: 400 }
      );
    }

    // Clean up expired codes first
    await deleteExpiredVerificationCodes();

    // Get verification code from database
    const verificationCode = await getVerificationCode(email, code, type);
    
    if (!verificationCode) {
      return NextResponse.json(
        { error: 'Invalid or expired verification code' },
        { status: 400 }
      );
    }

    // Check if code is already used (extra safety check)
    if (verificationCode.used) {
      return NextResponse.json(
        { error: 'Verification code has already been used' },
        { status: 400 }
      );
    }

    // Check if code has expired (extra safety check)
    const now = new Date();
    const expiresAt = new Date(verificationCode.expires_at);
    if (now > expiresAt) {
      return NextResponse.json(
        { error: 'Verification code has expired' },
        { status: 400 }
      );
    }

    // Mark code as used
    await markVerificationCodeAsUsed(verificationCode.id);

    return NextResponse.json(
      { 
        message: 'Email verification successful',
        email: email,
        verified: true
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error verifying code:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
