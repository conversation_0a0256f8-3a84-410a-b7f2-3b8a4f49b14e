import { NextRequest, NextResponse } from 'next/server';
import { 
  createVerificationC<PERSON>, 
  deleteVerificationCodesByEmail,
  getUserByUsername 
} from '@/lib/database';
import { generateVerificationCode, sendVerificationEmail } from '@/lib/email';

/**
 * POST /api/auth/send-verification - Send verification code to email
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, type, username } = body;

    // Validate required fields
    if (!email || !type) {
      return NextResponse.json(
        { error: 'Email and type are required' },
        { status: 400 }
      );
    }

    // Validate type
    if (type !== 'signup' && type !== 'signin') {
      return NextResponse.json(
        { error: 'Type must be either "signup" or "signin"' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // For signin, check if user exists and email matches
    let userId: number | undefined;
    if (type === 'signin') {
      if (!username) {
        return NextResponse.json(
          { error: 'Username is required for signin verification' },
          { status: 400 }
        );
      }

      const user = await getUserByUsername(username);
      if (!user) {
        return NextResponse.json(
          { error: 'User not found' },
          { status: 404 }
        );
      }

      if (user.gmail !== email) {
        return NextResponse.json(
          { error: 'Email does not match the registered email for this user' },
          { status: 400 }
        );
      }

      userId = user.id;
    }

    // Delete any existing verification codes for this email and type
    await deleteVerificationCodesByEmail(email, type);

    // Generate new verification code
    const code = generateVerificationCode();
    
    // Set expiration time (10 minutes from now)
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000).toISOString();

    // Save verification code to database
    await createVerificationCode(email, code, type, expiresAt, userId);

    // Send verification email
    try {
      await sendVerificationEmail(email, code, type, username);
    } catch (emailError) {
      console.error('Failed to send verification email:', emailError);
      return NextResponse.json(
        { error: 'Failed to send verification email. Please check your email configuration.' },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { 
        message: 'Verification code sent successfully',
        email: email,
        expiresIn: 600 // 10 minutes in seconds
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error sending verification code:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
