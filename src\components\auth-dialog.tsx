"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2, AlertCircle } from "lucide-react";
import { toast } from "sonner";
import { useUserAuth } from "@/hooks/use-local-storage";

interface AuthDialogProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function AuthDialog({ open, onClose, onSuccess }: AuthDialogProps) {
  const { authenticate } = useUserAuth();
  const [activeTab, setActiveTab] = useState("signin");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [verificationStep, setVerificationStep] = useState<{
    type: "signup" | "signin" | null;
    email: string;
    username: string;
    countdown: number;
  }>({ type: null, email: "", username: "", countdown: 0 });
  const [verificationLoading, setVerificationLoading] = useState(false);
  const [generatedRecoveryKey, setGeneratedRecoveryKey] = useState("");

  // Form states
  const [signInData, setSignInData] = useState({
    username: "",
    password: "",
    verificationCode: "",
  });
  const [signUpData, setSignUpData] = useState({
    username: "",
    password: "",
    recoveryKey: "",
    role: "regular" as "admin" | "regular",
    gmail: "",
    verificationCode: "",
  });
  const [forgotPasswordData, setForgotPasswordData] = useState({
    username: "",
    recoveryKey: "",
    newPassword: "",
  });

  const resetForms = () => {
    setSignInData({ username: "", password: "", verificationCode: "" });
    setSignUpData({
      username: "",
      password: "",
      recoveryKey: "",
      role: "regular",
      gmail: "",
      verificationCode: "",
    });
    setForgotPasswordData({ username: "", recoveryKey: "", newPassword: "" });
    setError("");
    setLoading(false);
    setGeneratedRecoveryKey("");
    setVerificationStep({ type: null, email: "", username: "", countdown: 0 });
    setVerificationLoading(false);
  };

  const handleClose = () => {
    resetForms();
    onClose();
  };

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    // Check if user is regular and needs email verification
    if (verificationStep.type !== "signin" && signInData.username) {
      // First, try to get user info to check if they're regular
      try {
        const userCheckResponse = await fetch("/api/auth/signin", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            username: signInData.username,
            password: signInData.password,
          }),
        });

        if (userCheckResponse.status === 400) {
          const userData = await userCheckResponse.json();
          if (
            userData.error ===
            "Email verification code is required for regular users"
          ) {
            // This is a regular user, send verification code
            await sendVerificationCode("", "signin", signInData.username);
            setLoading(false);
            return;
          }
        }
      } catch {
        // Continue with normal signin if check fails
      }
    }

    // If verification code is provided for regular users, include it
    const signinPayload = signInData.verificationCode
      ? signInData
      : { username: signInData.username, password: signInData.password };

    try {
      const response = await fetch("/api/auth/signin", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(signinPayload),
      });

      const data = await response.json();

      if (response.ok) {
        // Authenticate user with the new hook
        authenticate({
          id: data.user.id,
          username: data.user.username,
          role: data.user.role,
          gmail: data.user.gmail,
        });
        toast.success("Signed in successfully!");
        onSuccess();
        resetForms();
      } else {
        toast.error(data.error || "Sign in failed");
        setError(data.error || "Sign in failed");
      }
    } catch {
      toast.error("Network error. Please try again.");
      setError("Network error. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const sendVerificationCode = async (
    email: string,
    type: "signup" | "signin",
    username?: string
  ) => {
    setVerificationLoading(true);
    try {
      const response = await fetch("/api/auth/send-verification", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, type, username }),
      });

      const data = await response.json();

      if (response.ok) {
        setVerificationStep({
          type,
          email,
          username: username || "",
          countdown: 600, // 10 minutes
        });
        toast.success("Verification code sent to your email!");

        // Start countdown
        const interval = setInterval(() => {
          setVerificationStep((prev) => {
            if (prev.countdown <= 1) {
              clearInterval(interval);
              return { ...prev, countdown: 0 };
            }
            return { ...prev, countdown: prev.countdown - 1 };
          });
        }, 1000);
      } else {
        toast.error(data.error || "Failed to send verification code");
        setError(data.error || "Failed to send verification code");
      }
    } catch {
      toast.error("Network error. Please try again.");
      setError("Network error. Please try again.");
    } finally {
      setVerificationLoading(false);
    }
  };

  const verifyCode = async (
    email: string,
    code: string,
    type: "signup" | "signin"
  ) => {
    try {
      const response = await fetch("/api/auth/verify-code", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, code, type }),
      });

      const data = await response.json();

      if (response.ok) {
        return true;
      } else {
        toast.error(data.error || "Invalid verification code");
        setError(data.error || "Invalid verification code");
        return false;
      }
    } catch {
      toast.error("Network error. Please try again.");
      setError("Network error. Please try again.");
      return false;
    }
  };

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    // For regular users, handle email verification
    if (signUpData.role === "regular") {
      // If we haven't sent verification code yet, send it
      if (verificationStep.type !== "signup") {
        await sendVerificationCode(
          signUpData.gmail,
          "signup",
          signUpData.username
        );
        setLoading(false);
        return;
      }

      // If verification code is provided, verify it first
      if (signUpData.verificationCode) {
        const isValid = await verifyCode(
          signUpData.gmail,
          signUpData.verificationCode,
          "signup"
        );
        if (!isValid) {
          setLoading(false);
          return;
        }
      } else {
        setError("Please enter the verification code sent to your email");
        setLoading(false);
        return;
      }
    }

    try {
      const response = await fetch("/api/auth/signup", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(signUpData),
      });

      const data = await response.json();

      if (response.ok) {
        // If a recovery key was generated, show it to the user
        if (
          data.recoveryKey &&
          (!signUpData.recoveryKey || signUpData.recoveryKey.trim() === "")
        ) {
          setGeneratedRecoveryKey(data.recoveryKey);
          toast.success(
            `Account created successfully! Recovery key: ${data.recoveryKey}`,
            {
              duration: 10000, // Show for 10 seconds
            }
          );
          alert(
            `Account created successfully!\n\nYour auto-generated recovery key is: ${data.recoveryKey}\n\nPlease save this recovery key in a secure location. You will need it to reset your password if you forget it.`
          );
        } else {
          toast.success("Account created successfully!");
        }
        // Authenticate user with the new hook
        authenticate({
          id: data.user.id,
          username: data.user.username,
          role: data.user.role,
          gmail: data.user.gmail,
        });
        onSuccess();
        resetForms();
      } else {
        toast.error(data.error || "Sign up failed");
        setError(data.error || "Sign up failed");
      }
    } catch {
      toast.error("Network error. Please try again.");
      setError("Network error. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      const response = await fetch("/api/auth/reset-password", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(forgotPasswordData),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success(
          "Password reset successfully! Please sign in with your new password."
        );
        setError("");
        setActiveTab("signin");
        alert(
          "Password reset successfully! Please sign in with your new password."
        );
      } else {
        toast.error(data.error || "Password reset failed");
        setError(data.error || "Password reset failed");
      }
    } catch {
      toast.error("Network error. Please try again.");
      setError("Network error. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Admin Authentication</DialogTitle>
          <DialogDescription>
            Sign in to enable admin mode and access administrative features.
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="signin">Sign In</TabsTrigger>
            <TabsTrigger value="signup">Sign Up</TabsTrigger>
            <TabsTrigger value="forgot">Forgot Password</TabsTrigger>
          </TabsList>

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <TabsContent value="signin" className="space-y-4">
            <form onSubmit={handleSignIn} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="signin-username">Username</Label>
                <Input
                  id="signin-username"
                  type="text"
                  value={signInData.username}
                  onChange={(e) =>
                    setSignInData({ ...signInData, username: e.target.value })
                  }
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="signin-password">Password</Label>
                <Input
                  id="signin-password"
                  type="password"
                  value={signInData.password}
                  onChange={(e) =>
                    setSignInData({ ...signInData, password: e.target.value })
                  }
                  required
                />
              </div>

              {/* Verification Code Input for Regular Users */}
              {verificationStep.type === "signin" && (
                <div className="space-y-2">
                  <Label htmlFor="signin-verification">
                    Email Verification Code
                  </Label>
                  <Input
                    id="signin-verification"
                    type="text"
                    value={signInData.verificationCode}
                    onChange={(e) =>
                      setSignInData({
                        ...signInData,
                        verificationCode: e.target.value,
                      })
                    }
                    placeholder="Enter 6-digit code"
                    maxLength={6}
                    required
                  />
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <span>Check your email for the verification code</span>
                    {verificationStep.countdown > 0 && (
                      <span>
                        Expires in {Math.floor(verificationStep.countdown / 60)}
                        :
                        {(verificationStep.countdown % 60)
                          .toString()
                          .padStart(2, "0")}
                      </span>
                    )}
                  </div>
                  {verificationStep.countdown === 0 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        sendVerificationCode("", "signin", signInData.username)
                      }
                      disabled={verificationLoading}
                    >
                      {verificationLoading ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        "Resend Code"
                      )}
                    </Button>
                  )}
                </div>
              )}

              <Button type="submit" className="w-full" disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Sign In
              </Button>
            </form>
          </TabsContent>

          <TabsContent value="signup" className="space-y-4">
            <form onSubmit={handleSignUp} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="signup-username">Username</Label>
                <Input
                  id="signup-username"
                  type="text"
                  value={signUpData.username}
                  onChange={(e) =>
                    setSignUpData({ ...signUpData, username: e.target.value })
                  }
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="signup-password">Password</Label>
                <Input
                  id="signup-password"
                  type="password"
                  value={signUpData.password}
                  onChange={(e) =>
                    setSignUpData({ ...signUpData, password: e.target.value })
                  }
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="signup-role">User Role</Label>
                <Select
                  value={signUpData.role}
                  onValueChange={(value: "admin" | "regular") =>
                    setSignUpData({ ...signUpData, role: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select user role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="admin">Admin</SelectItem>
                    <SelectItem value="regular">Regular User</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  Admin users can manage templates and approve documents.
                  Regular users can apply for certificates.
                </p>
              </div>
              {signUpData.role === "regular" && (
                <div className="space-y-2">
                  <Label htmlFor="signup-gmail">Gmail Account</Label>
                  <Input
                    id="signup-gmail"
                    type="email"
                    value={signUpData.gmail}
                    onChange={(e) =>
                      setSignUpData({ ...signUpData, gmail: e.target.value })
                    }
                    placeholder="<EMAIL>"
                    required
                  />
                  <p className="text-xs text-muted-foreground">
                    Gmail account is required for regular users to receive
                    notifications.
                  </p>
                </div>
              )}

              {/* Verification Code Input for Regular Users */}
              {signUpData.role === "regular" &&
                verificationStep.type === "signup" && (
                  <div className="space-y-2">
                    <Label htmlFor="signup-verification">
                      Email Verification Code
                    </Label>
                    <Input
                      id="signup-verification"
                      type="text"
                      value={signUpData.verificationCode}
                      onChange={(e) =>
                        setSignUpData({
                          ...signUpData,
                          verificationCode: e.target.value,
                        })
                      }
                      placeholder="Enter 6-digit code"
                      maxLength={6}
                      required
                    />
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>Check your email for the verification code</span>
                      {verificationStep.countdown > 0 && (
                        <span>
                          Expires in{" "}
                          {Math.floor(verificationStep.countdown / 60)}:
                          {(verificationStep.countdown % 60)
                            .toString()
                            .padStart(2, "0")}
                        </span>
                      )}
                    </div>
                    {verificationStep.countdown === 0 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() =>
                          sendVerificationCode(
                            signUpData.gmail,
                            "signup",
                            signUpData.username
                          )
                        }
                        disabled={verificationLoading}
                      >
                        {verificationLoading ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          "Resend Code"
                        )}
                      </Button>
                    )}
                  </div>
                )}

              <div className="space-y-2">
                <Label htmlFor="signup-recovery">Recovery Key (Optional)</Label>
                <Input
                  id="signup-recovery"
                  type="text"
                  value={signUpData.recoveryKey}
                  onChange={(e) =>
                    setSignUpData({
                      ...signUpData,
                      recoveryKey: e.target.value,
                    })
                  }
                  placeholder="Leave empty to auto-generate"
                />
                <p className="text-xs text-muted-foreground">
                  If left empty, a recovery key will be automatically generated
                  for you.
                </p>
              </div>
              <Button type="submit" className="w-full" disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Sign Up
              </Button>
            </form>
          </TabsContent>

          <TabsContent value="forgot" className="space-y-4">
            <form onSubmit={handleForgotPassword} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="forgot-username">Username</Label>
                <Input
                  id="forgot-username"
                  type="text"
                  value={forgotPasswordData.username}
                  onChange={(e) =>
                    setForgotPasswordData({
                      ...forgotPasswordData,
                      username: e.target.value,
                    })
                  }
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="forgot-recovery">Recovery Key</Label>
                <Input
                  id="forgot-recovery"
                  type="text"
                  value={forgotPasswordData.recoveryKey}
                  onChange={(e) =>
                    setForgotPasswordData({
                      ...forgotPasswordData,
                      recoveryKey: e.target.value,
                    })
                  }
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="forgot-new-password">New Password</Label>
                <Input
                  id="forgot-new-password"
                  type="password"
                  value={forgotPasswordData.newPassword}
                  onChange={(e) =>
                    setForgotPasswordData({
                      ...forgotPasswordData,
                      newPassword: e.target.value,
                    })
                  }
                  required
                />
              </div>
              <Button type="submit" className="w-full" disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Reset Password
              </Button>
            </form>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
